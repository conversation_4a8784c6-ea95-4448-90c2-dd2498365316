import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

describe('App Authentication', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    sessionStorage.clear();
    localStorage.clear();
  });

  it('shows login screen when no session exists', () => {
    // Mock empty sessionStorage
    const mockSessionStorage = {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null
    };
    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true
    });

    render(<App />);

    expect(screen.getByText('Momentum')).toBeInTheDocument();
    expect(screen.getByText('Welcome to Momentum')).toBeInTheDocument();
    expect(screen.getByText('Please authenticate to continue')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create New Identity' })).toBeInTheDocument();
  });

  it('renders the app with valid authenticated session', () => {
    // Mock valid sessionStorage with timestamp
    const mockSessionStorage = {
      getItem: (key: string) => {
        if (key === 'momentum_session') {
          return JSON.stringify({
            name: 'Test User',
            publicKey: 'test-public-key-12345',
            secretKey: 'test-secret-key',
            timestamp: Date.now() // Current timestamp
          });
        }
        return null;
      },
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null
    };

    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true
    });

    // Mock localStorage for user data
    const mockLocalStorage = {
      getItem: () => '[]',
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null
    };

    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    render(<App />);

    // Check if the main app elements are present
    expect(screen.getByText('Momentum')).toBeInTheDocument();
    expect(screen.getByText('Master Dashboard')).toBeInTheDocument();
    // User name should appear in both the sidebar and header
    expect(screen.getAllByText('Test User')).toHaveLength(2);
  });

  it('shows error message for expired session', () => {
    // Mock expired sessionStorage
    const expiredTimestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
    const mockSessionStorage = {
      getItem: (key: string) => {
        if (key === 'momentum_session') {
          return JSON.stringify({
            name: 'Test User',
            publicKey: 'test-public-key-12345',
            secretKey: 'test-secret-key',
            timestamp: expiredTimestamp
          });
        }
        return null;
      },
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null
    };

    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true
    });

    render(<App />);

    expect(screen.getByText('Momentum')).toBeInTheDocument();
    expect(screen.getByText('Your session has expired. Please login again.')).toBeInTheDocument();
  });

  it('shows error message for invalid session data', () => {
    // Mock invalid sessionStorage
    const mockSessionStorage = {
      getItem: (key: string) => {
        if (key === 'momentum_session') {
          return JSON.stringify({
            name: 'Test User',
            // Missing publicKey
            secretKey: 'test-secret-key',
            timestamp: Date.now()
          });
        }
        return null;
      },
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null
    };

    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true
    });

    render(<App />);

    expect(screen.getByText('Momentum')).toBeInTheDocument();
    expect(screen.getByText('Invalid session data. Please login again.')).toBeInTheDocument();
  });
});
