import React from 'react';
import { Plus, ChevronsLeft, ChevronsRight } from 'lucide-react';
import type { HeaderProps } from '../../types';

export const Header: React.FC<HeaderProps> = ({ onAddTask, isDrawerOpen, toggleDrawer, activeProject, currentView }) => {
    const viewTitles: Record<string, string> = {
        'Dashboard': 'Dashboard',
        'Day': 'Day Planner',
        'Board': activeProject?.name || '',
        'List': activeProject?.name || '',
        'Calendar': activeProject?.name || '',
        'Gantt': activeProject?.name || ''
    };
    
    return (
        <header className="flex items-center justify-between p-4 border-b border-gray-700/80 bg-gray-900/80 backdrop-blur-sm z-10 flex-shrink-0">
            <div className="flex items-center space-x-4">
                <button onClick={toggleDrawer} className="p-2 rounded-md hover:bg-gray-700">
                    {isDrawerOpen ? <ChevronsLeft size={20}/> : <ChevronsRight size={20} />}
                </button>
                <h1 className="text-2xl font-bold text-white">{viewTitles[currentView] || 'Momentum'}</h1>
            </div>
            <div className="flex items-center space-x-4">
                {activeProject &&
                    <button 
                        onClick={onAddTask} 
                        className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-75"
                    >
                        <Plus size={18} />
                        <span className="hidden md:inline">New Task</span>
                    </button>
                }
            </div>
        </header>
    );
};
