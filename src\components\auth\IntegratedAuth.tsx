import React, { useState, useEffect } from 'react';
import { User, Lock, UserPlus } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import { IdentityModal } from './IdentityModal';
import type { StoredIdentity, User as UserType } from '../../types';

interface IntegratedAuthProps {
    authError?: string | null;
    onLoginSuccess: (user: UserType) => void;
}

export const IntegratedAuth: React.FC<IntegratedAuthProps> = ({ authError, onLoginSuccess }) => {
    const [identities, setIdentities] = useState<StoredIdentity[]>([]);
    const [selectedIdentity, setSelectedIdentity] = useState<StoredIdentity | null>(null);
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const { loadIdentities, loginWithIdentity } = useIdentityManagement();

    useEffect(() => {
        const storedIdentities = loadIdentities();
        setIdentities(storedIdentities);
        
        // Auto-select the first identity if there's only one
        if (storedIdentities.length === 1) {
            setSelectedIdentity(storedIdentities[0]);
        }
    }, [loadIdentities]);

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedIdentity || !password) return;

        setIsLoading(true);
        const result = await loginWithIdentity(selectedIdentity, password);
        
        if (result.success && result.user) {
            onLoginSuccess(result.user);
        } else {
            alert(result.error || 'Login failed. Please try again.');
        }
        
        setIsLoading(false);
        setPassword('');
    };

    const handleIdentityCreated = (user: UserType) => {
        onLoginSuccess(user);
    };

    return (
        <div className="flex-1 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Lock size={32} className="text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-white mb-2">Welcome to Momentum</h1>
                    <p className="text-gray-400">Please authenticate to continue</p>
                    {authError && (
                        <div className="mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 text-sm">
                            {authError}
                        </div>
                    )}
                </div>

                {identities.length > 0 ? (
                    <div className="space-y-6">
                        {/* Identity Selection */}
                        {identities.length > 1 && (
                            <div>
                                <label className="block text-gray-300 text-sm font-medium mb-3">
                                    Choose Identity
                                </label>
                                <div className="space-y-2">
                                    {identities.map((identity, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setSelectedIdentity(identity)}
                                            className={`w-full text-left p-3 rounded-lg transition-colors flex items-center space-x-3 ${
                                                selectedIdentity?.publicKey === identity.publicKey
                                                    ? 'bg-indigo-600 text-white'
                                                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                                            }`}
                                        >
                                            <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                                                <User size={20} className="text-white" />
                                            </div>
                                            <div>
                                                <p className="font-medium">{identity.name}</p>
                                                <p className="text-xs opacity-75 font-mono">{identity.publicKey.substring(0, 24)}...</p>
                                            </div>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Password Input */}
                        {selectedIdentity && (
                            <form onSubmit={handleLogin}>
                                <div className="mb-4">
                                    <label htmlFor="password" className="block text-gray-300 text-sm font-medium mb-2">
                                        Password for {selectedIdentity.name}
                                    </label>
                                    <input
                                        type="password"
                                        id="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                        placeholder="Enter your password"
                                        required
                                        disabled={isLoading}
                                        autoFocus
                                    />
                                </div>

                                <button
                                    type="submit"
                                    className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:bg-gray-600 mb-4"
                                    disabled={isLoading || !password}
                                >
                                    {isLoading ? 'Signing in...' : 'Sign In'}
                                </button>
                            </form>
                        )}

                        {/* Create New Identity */}
                        <div className="text-center">
                            <p className="text-gray-400 text-sm mb-3">Need a new identity?</p>
                            <button
                                onClick={() => setShowCreateModal(true)}
                                className="inline-flex items-center space-x-2 text-indigo-400 hover:text-indigo-300 font-medium"
                            >
                                <UserPlus size={16} />
                                <span>Create New Identity</span>
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="text-center">
                        <p className="text-gray-400 mb-6">No identities found. Create your first identity to get started.</p>
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                        >
                            <UserPlus size={18} />
                            <span>Create New Identity</span>
                        </button>
                    </div>
                )}

                <div className="text-center mt-8 text-sm text-gray-400">
                    <p>🔒 Your identity is stored securely on your device</p>
                    <p>🌐 No data is sent to external servers</p>
                </div>
            </div>

            <IdentityModal
                isOpen={showCreateModal}
                onClose={() => setShowCreateModal(false)}
                onIdentityCreated={handleIdentityCreated}
            />
        </div>
    );
};
